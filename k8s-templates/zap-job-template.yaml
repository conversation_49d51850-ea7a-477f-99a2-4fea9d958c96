apiVersion: batch/v1
kind: Job
metadata:
  name: zap-scan-{{CLIENT_ID}}
  namespace: {{NAMESPACE}}
  labels:
    app: zap-scan
    clientId: "{{CLIENT_ID}}"
    component: security-testing
    managed-by: agentq-websocket-service
spec:
  template:
    metadata:
      labels:
        job: zap-scan-{{CLIENT_ID}}
        app: zap-scan
        clientId: "{{CLIENT_ID}}"
    spec:
      restartPolicy: Never
      containers:
      - name: zap-container
        image: {{ZAP_IMAGE}}
        command: ["zap.sh"]
        args:
          - "-daemon"
          - "-host"
          - "0.0.0.0"
          - "-port"
          - "{{ZAP_PORT}}"
          - "-config"
          - "api.addrs.addr.name=.*"
          - "-config"
          - "api.addrs.addr.regex=true"
          - "-config"
          - "api.key={{ZAP_API_KEY}}"
        ports:
        - containerPort: {{ZAP_PORT}}
          protocol: TCP
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1"
        env:
        - name: CLIENT_ID
          value: "{{CLIENT_ID}}"
        - name: ZAP_API_KEY
          value: "{{ZAP_API_KEY}}"
        # Health check to ensure ZAP is ready
        readinessProbe:
          httpGet:
            path: /JSON/core/view/version/
            port: {{ZAP_PORT}}
            httpHeaders:
            - name: X-ZAP-API-Key
              value: "{{ZAP_API_KEY}}"
          initialDelaySeconds: 30
          periodSeconds: 5
          timeoutSeconds: 10
          failureThreshold: 12  # Allow up to 1 minute for startup
        # Liveness probe to restart if ZAP becomes unresponsive
        livenessProbe:
          httpGet:
            path: /JSON/core/view/version/
            port: {{ZAP_PORT}}
            httpHeaders:
            - name: X-ZAP-API-Key
              value: "{{ZAP_API_KEY}}"
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        # Security context for better isolation
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
  # Job configuration
  backoffLimit: 1  # Don't retry failed jobs
  ttlSecondsAfterFinished: 300  # Clean up completed jobs after 5 minutes
  activeDeadlineSeconds: 3600   # Kill job if it runs longer than 1 hour
---
apiVersion: v1
kind: Service
metadata:
  name: zap-service-{{CLIENT_ID}}
  namespace: {{NAMESPACE}}
  labels:
    app: zap-scan
    clientId: "{{CLIENT_ID}}"
    component: security-testing
    managed-by: agentq-websocket-service
spec:
  selector:
    job: zap-scan-{{CLIENT_ID}}
  ports:
  - port: {{ZAP_PORT}}
    targetPort: {{ZAP_PORT}}
    protocol: TCP
  type: ClusterIP
