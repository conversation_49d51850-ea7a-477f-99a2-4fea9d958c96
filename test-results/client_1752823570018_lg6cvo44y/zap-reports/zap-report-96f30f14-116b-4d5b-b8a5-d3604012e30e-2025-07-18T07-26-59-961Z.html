<!DOCTYPE html>
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>ZAP Scanning Report</title>
<style type="text/css">
body {
	font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
	color: #000;
	font-size: 13px;
}

h1 {
	text-align: center;
	font-weight: bold;
	font-size: 32px
}

h3 {
	font-size: 16px;
}

table {
	border: none;
	font-size: 13px;
}

td, th {
	padding: 3px 4px;
	word-break: break-word;
}

th {
	font-weight: bold;
	background-color: #666666;
}

td {
	background-color: #e8e8e8;
}

.spacer {
	margin: 10px;
}

.spacer-lg {
	margin: 40px;
}

.indent1 {
	padding: 4px 20px;
}

.indent2 {
	padding: 4px 40px;
}

.risk-3 {
	background-color: red;
	color: #FFF;
}

.risk-2 {
	background-color: orange;
	color: #FFF;
}

.risk-1 {
	background-color: yellow;
	color: #000;
}

.risk-0 {
	background-color: blue;
	color: #FFF;
}

.risk--1 {
	background-color: green;
	color: #FFF;
}

.summary {
	width: 45%;
}

.summary th {
	color: #FFF;
}

.summary100 {
	width: 100%;
}

.summary80 {
	width: 80%;
}

.tdconstrainer {
	width: 9.1%;
	padding: 0
}

.alerts {
	width: 75%;
}

.alerts th {
	color: #FFF;
}

.results {
	width: 100%;
}

.results th {
	text-align: left;
}

.left-header {
	display: inline-block;
}

.pass {
	background: green;
	color: white
}

.pass::before {
	content: '\2714';
	color: white
}

.fail {
	background: red;
	color: white
}

.fail::before {
	content: '\2716';
	color: white
}

.alert-3b::before {
	content: '\2691';
	color: red
}

.alert-2b::before {
	content: '\2691 ';
	color: orange
}

.alert-1b::before {
	content: '\2691';
	color: yellow
}

.alert-0b::before {
	content: '\2691';
	color: blue
}

.alert-3a::after {
	content: '\2691';
	color: red
}

.alert-2a::after {
	content: '\2691 ';
	color: orange
}

.alert-1a::after {
	content: '\2691';
	color: yellow
}

.alert-0a::after {
	content: '\2691';
	color: blue
}

.lm2 {
	margin-left: 2em;
}

.smallnote {
	font-size: 0.75em
}

.alwayswhite {
	color: white
}
</style>
</head>
<body>
	<h1>
		<!-- The ZAP by Checkmark Logo -->
		<img
			src="data:image/png;base64,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"
			alt="" />
		ZAP Scanning Report
	</h1>
	<p />
	

	<h2>
		
		
		Sites: https://events.backtrace.io https://fonts.gstatic.com https://fonts.googleapis.com https://www.saucedemo.com
	</h2>

	<h3>
		Generated on Fri, 18 Jul 2025 07:26:59
	</h3>

	<h3>
		ZAP Version: 2.16.1
	</h3>

	<h4>
		ZAP by <a href="https://checkmarx.com/">Checkmarx</a>
	</h4>

	
		<h3 class="left-header">Summary of Alerts</h3>
		<table class="summary">
			<tr>
				<th width="45%"
					height="24">Risk Level</th>
				<th width="55%"
					align="center">Number of Alerts</th>
			</tr>
			<tr>
				<td class="risk-3">
					<div>High</div>
				</td>
				<td align="center">
					<div>0</div>
				</td>
			</tr>
			<tr>
				<td class="risk-2">
					<div>Medium</div>
				</td>
				<td align="center">
					<div>3</div>
				</td>
			</tr>
			<tr>
				<td class="risk-1">
					<div>Low</div>
				</td>
				<td align="center">
					<div>2</div>
				</td>
			</tr>
			<tr>
				<td class="risk-0">
					<div>Informational</div>
				</td>
				<td align="center">
					<div>5</div>
				</td>
			</tr>
			<tr>
				<td class="risk--1">
					<div>				False Positives:</div>
				</td>
				<td align="center">
					<div>0</div>
				</td>
			</tr>
		</table>
		<div class="spacer-lg"></div>
	

	
		
			<h3>Summary of Sequences</h3>
			<p class="smallnote">For each step: result (Pass/Fail) - risk (of highest alert(s) for the step, if any).</p>

			
		
	

	
		<h3>Alerts</h3>
		<table class="alerts">
			<tr>
				<th width="60%" height="24">Name</th>
				<th width="20%"
					align="center">Risk Level</th>
				<th width="20%"
					align="center">Number of Instances</th>
			</tr>
			<tr>
				<td><a href="#10038">Content Security Policy (CSP) Header Not Set</a></td>
				<td align="center" class="risk-2">Medium</td>
				<td align="center">1</td>
			</tr>
			<tr>
				<td><a href="#10098">Cross-Domain Misconfiguration</a></td>
				<td align="center" class="risk-2">Medium</td>
				<td align="center">9</td>
			</tr>
			<tr>
				<td><a href="#10020">Missing Anti-clickjacking Header</a></td>
				<td align="center" class="risk-2">Medium</td>
				<td align="center">1</td>
			</tr>
			<tr>
				<td><a href="#10035">Strict-Transport-Security Header Not Set</a></td>
				<td align="center" class="risk-1">Low</td>
				<td align="center">12</td>
			</tr>
			<tr>
				<td><a href="#10021">X-Content-Type-Options Header Missing</a></td>
				<td align="center" class="risk-1">Low</td>
				<td align="center">5</td>
			</tr>
			<tr>
				<td><a href="#10024">Information Disclosure - Sensitive Information in URL</a></td>
				<td align="center" class="risk-0">Informational</td>
				<td align="center">4</td>
			</tr>
			<tr>
				<td><a href="#10027">Information Disclosure - Suspicious Comments</a></td>
				<td align="center" class="risk-0">Informational</td>
				<td align="center">2</td>
			</tr>
			<tr>
				<td><a href="#10109">Modern Web Application</a></td>
				<td align="center" class="risk-0">Informational</td>
				<td align="center">1</td>
			</tr>
			<tr>
				<td><a href="#10015">Re-examine Cache-control Directives</a></td>
				<td align="center" class="risk-0">Informational</td>
				<td align="center">1</td>
			</tr>
			<tr>
				<td><a href="#10050">Retrieved from Cache</a></td>
				<td align="center" class="risk-0">Informational</td>
				<td align="center">8</td>
			</tr>
		</table>
		<div class="spacer-lg"></div>
	

	
		<h3>Alert Detail</h3>
		
			<table class="results">
				<tr height="24">
					<th width="20%" class="risk-2"><a
						id="10038"></a>
						<div>Medium</div></th>
					<th class="risk-2">Content Security Policy (CSP) Header Not Set</th>
				</tr>
				<tr>
					<td width="20%">Description</td>
					<td width="80%">
							<div>Content Security Policy (CSP) is an added layer of security that helps to detect and mitigate certain types of attacks, including Cross Site Scripting (XSS) and data injection attacks. These attacks are used for everything from data theft to site defacement or distribution of malware. CSP provides a set of standard HTTP headers that allow website owners to declare approved sources of content that browsers should be allowed to load on that page — covered types are JavaScript, CSS, HTML frames, fonts, images and embeddable objects such as Java applets, ActiveX, audio and video files.</div>
							
						</td>
				</tr>
				<TR vAlign="top">
					<TD colspan="2"></TD>
				</TR>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/">https://www.saucedemo.com/</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
				<tr>
					<td width="20%">Instances</td>
					<td width="80%">1</td>
				</tr>
				<tr>
					<td width="20%">Solution</td>
					<td width="80%">
							<div>Ensure that your web server, application server, load balancer, etc. is configured to set the Content-Security-Policy header.</div>
							
						</td>
				</tr>
				<tr>
					<td width="20%">Reference</td>
					<td width="80%">
							<a href="https://developer.mozilla.org/en-US/docs/Web/Security/CSP/Introducing_Content_Security_Policy">https://developer.mozilla.org/en-US/docs/Web/Security/CSP/Introducing_Content_Security_Policy</a>
							<br />
						
							<a href="https://cheatsheetseries.owasp.org/cheatsheets/Content_Security_Policy_Cheat_Sheet.html">https://cheatsheetseries.owasp.org/cheatsheets/Content_Security_Policy_Cheat_Sheet.html</a>
							<br />
						
							<a href="https://www.w3.org/TR/CSP/">https://www.w3.org/TR/CSP/</a>
							<br />
						
							<a href="https://w3c.github.io/webappsec-csp/">https://w3c.github.io/webappsec-csp/</a>
							<br />
						
							<a href="https://web.dev/articles/csp">https://web.dev/articles/csp</a>
							<br />
						
							<a href="https://caniuse.com/#feat=contentsecuritypolicy">https://caniuse.com/#feat=contentsecuritypolicy</a>
							<br />
						
							<a href="https://content-security-policy.com/">https://content-security-policy.com/</a>
							
						</td>
				</tr>
				<tr>
					<td width="20%">CWE Id</td>
					<td width="80%"><a
						href="https://cwe.mitre.org/data/definitions/693.html">693</a></td>
				</tr>
				<tr>
					<td width="20%">WASC Id</td>
					<td width="80%">15</td>
				</tr>
				<tr>
					<td width="20%">Plugin Id</td>
					<td width="80%"><a
						href="https://www.zaproxy.org/docs/alerts/10038/">10038</a></td>
				</tr>
			</table>
			<div class="spacer"></div>
		
			<table class="results">
				<tr height="24">
					<th width="20%" class="risk-2"><a
						id="10098"></a>
						<div>Medium</div></th>
					<th class="risk-2">Cross-Domain Misconfiguration</th>
				</tr>
				<tr>
					<td width="20%">Description</td>
					<td width="80%">
							<div>Web browser data loading may be possible, due to a Cross Origin Resource Sharing (CORS) misconfiguration on the web server.</div>
							
						</td>
				</tr>
				<TR vAlign="top">
					<TD colspan="2"></TD>
				</TR>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://fonts.googleapis.com/css2?family=DM+Mono:wght@400;500&amp;family=DM+Sans:wght@400;500">https://fonts.googleapis.com/css2?family=DM+Mono:wght@400;500&amp;family=DM+Sans:wght@400;500</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">Access-Control-Allow-Origin: *</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The CORS misconfiguration on the web server permits cross-domain read requests from arbitrary third party domains, using unauthenticated APIs on this domain. Web browser implementations do not permit arbitrary third parties to read the response from authenticated APIs, however. This reduces the risk somewhat. This misconfiguration could be used by an attacker to access data that is available in an unauthenticated manner, but which uses some other form of security, such as IP address white-listing.</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://fonts.gstatic.com/s/dmmono/v15/aFTR7PB1QTsUX8KYvumzEYOtbYf-Vlg.woff2">https://fonts.gstatic.com/s/dmmono/v15/aFTR7PB1QTsUX8KYvumzEYOtbYf-Vlg.woff2</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">Access-Control-Allow-Origin: *</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The CORS misconfiguration on the web server permits cross-domain read requests from arbitrary third party domains, using unauthenticated APIs on this domain. Web browser implementations do not permit arbitrary third parties to read the response from authenticated APIs, however. This reduces the risk somewhat. This misconfiguration could be used by an attacker to access data that is available in an unauthenticated manner, but which uses some other form of security, such as IP address white-listing.</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://fonts.gstatic.com/s/dmmono/v15/aFTU7PB1QTsUX8KYthqQBK6PYK0.woff2">https://fonts.gstatic.com/s/dmmono/v15/aFTU7PB1QTsUX8KYthqQBK6PYK0.woff2</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">Access-Control-Allow-Origin: *</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The CORS misconfiguration on the web server permits cross-domain read requests from arbitrary third party domains, using unauthenticated APIs on this domain. Web browser implementations do not permit arbitrary third parties to read the response from authenticated APIs, however. This reduces the risk somewhat. This misconfiguration could be used by an attacker to access data that is available in an unauthenticated manner, but which uses some other form of security, such as IP address white-listing.</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://fonts.gstatic.com/s/dmsans/v16/rP2Yp2ywxg089UriI5-g4vlH9VoD8Cmcqbu0-K6z9mXg.woff2">https://fonts.gstatic.com/s/dmsans/v16/rP2Yp2ywxg089UriI5-g4vlH9VoD8Cmcqbu0-K6z9mXg.woff2</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">Access-Control-Allow-Origin: *</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The CORS misconfiguration on the web server permits cross-domain read requests from arbitrary third party domains, using unauthenticated APIs on this domain. Web browser implementations do not permit arbitrary third parties to read the response from authenticated APIs, however. This reduces the risk somewhat. This misconfiguration could be used by an attacker to access data that is available in an unauthenticated manner, but which uses some other form of security, such as IP address white-listing.</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/">https://www.saucedemo.com/</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">Access-Control-Allow-Origin: *</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The CORS misconfiguration on the web server permits cross-domain read requests from arbitrary third party domains, using unauthenticated APIs on this domain. Web browser implementations do not permit arbitrary third parties to read the response from authenticated APIs, however. This reduces the risk somewhat. This misconfiguration could be used by an attacker to access data that is available in an unauthenticated manner, but which uses some other form of security, such as IP address white-listing.</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/service-worker.js">https://www.saucedemo.com/service-worker.js</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">Access-Control-Allow-Origin: *</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The CORS misconfiguration on the web server permits cross-domain read requests from arbitrary third party domains, using unauthenticated APIs on this domain. Web browser implementations do not permit arbitrary third parties to read the response from authenticated APIs, however. This reduces the risk somewhat. This misconfiguration could be used by an attacker to access data that is available in an unauthenticated manner, but which uses some other form of security, such as IP address white-listing.</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/static/css/main.f6c64be5.chunk.css">https://www.saucedemo.com/static/css/main.f6c64be5.chunk.css</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">Access-Control-Allow-Origin: *</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The CORS misconfiguration on the web server permits cross-domain read requests from arbitrary third party domains, using unauthenticated APIs on this domain. Web browser implementations do not permit arbitrary third parties to read the response from authenticated APIs, however. This reduces the risk somewhat. This misconfiguration could be used by an attacker to access data that is available in an unauthenticated manner, but which uses some other form of security, such as IP address white-listing.</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/static/js/2.84a5cbf3.chunk.js">https://www.saucedemo.com/static/js/2.84a5cbf3.chunk.js</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">Access-Control-Allow-Origin: *</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The CORS misconfiguration on the web server permits cross-domain read requests from arbitrary third party domains, using unauthenticated APIs on this domain. Web browser implementations do not permit arbitrary third parties to read the response from authenticated APIs, however. This reduces the risk somewhat. This misconfiguration could be used by an attacker to access data that is available in an unauthenticated manner, but which uses some other form of security, such as IP address white-listing.</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/static/js/main.018d2d1e.chunk.js">https://www.saucedemo.com/static/js/main.018d2d1e.chunk.js</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">Access-Control-Allow-Origin: *</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The CORS misconfiguration on the web server permits cross-domain read requests from arbitrary third party domains, using unauthenticated APIs on this domain. Web browser implementations do not permit arbitrary third parties to read the response from authenticated APIs, however. This reduces the risk somewhat. This misconfiguration could be used by an attacker to access data that is available in an unauthenticated manner, but which uses some other form of security, such as IP address white-listing.</td>
					</tr>
				
				<tr>
					<td width="20%">Instances</td>
					<td width="80%">9</td>
				</tr>
				<tr>
					<td width="20%">Solution</td>
					<td width="80%">
							<div>Ensure that sensitive data is not available in an unauthenticated manner (using IP address white-listing, for instance).</div>
							<br />
						
							<div>Configure the &quot;Access-Control-Allow-Origin&quot; HTTP header to a more restrictive set of domains, or remove all CORS headers entirely, to allow the web browser to enforce the Same Origin Policy (SOP) in a more restrictive manner.</div>
							
						</td>
				</tr>
				<tr>
					<td width="20%">Reference</td>
					<td width="80%">
							<a href="https://vulncat.fortify.com/en/detail?id=desc.config.dotnet.html5_overly_permissive_cors_policy">https://vulncat.fortify.com/en/detail?id=desc.config.dotnet.html5_overly_permissive_cors_policy</a>
							
						</td>
				</tr>
				<tr>
					<td width="20%">CWE Id</td>
					<td width="80%"><a
						href="https://cwe.mitre.org/data/definitions/264.html">264</a></td>
				</tr>
				<tr>
					<td width="20%">WASC Id</td>
					<td width="80%">14</td>
				</tr>
				<tr>
					<td width="20%">Plugin Id</td>
					<td width="80%"><a
						href="https://www.zaproxy.org/docs/alerts/10098/">10098</a></td>
				</tr>
			</table>
			<div class="spacer"></div>
		
			<table class="results">
				<tr height="24">
					<th width="20%" class="risk-2"><a
						id="10020"></a>
						<div>Medium</div></th>
					<th class="risk-2">Missing Anti-clickjacking Header</th>
				</tr>
				<tr>
					<td width="20%">Description</td>
					<td width="80%">
							<div>The response does not protect against &#39;ClickJacking&#39; attacks. It should include either Content-Security-Policy with &#39;frame-ancestors&#39; directive or X-Frame-Options.</div>
							
						</td>
				</tr>
				<TR vAlign="top">
					<TD colspan="2"></TD>
				</TR>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/">https://www.saucedemo.com/</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%">x-frame-options</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
				<tr>
					<td width="20%">Instances</td>
					<td width="80%">1</td>
				</tr>
				<tr>
					<td width="20%">Solution</td>
					<td width="80%">
							<div>Modern Web browsers support the Content-Security-Policy and X-Frame-Options HTTP headers. Ensure one of them is set on all web pages returned by your site/app.</div>
							<br />
						
							<div>If you expect the page to be framed only by pages on your server (e.g. it&#39;s part of a FRAMESET) then you&#39;ll want to use SAMEORIGIN, otherwise if you never expect the page to be framed, you should use DENY. Alternatively consider implementing Content Security Policy&#39;s &quot;frame-ancestors&quot; directive.</div>
							
						</td>
				</tr>
				<tr>
					<td width="20%">Reference</td>
					<td width="80%">
							<a href="https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Frame-Options">https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Frame-Options</a>
							
						</td>
				</tr>
				<tr>
					<td width="20%">CWE Id</td>
					<td width="80%"><a
						href="https://cwe.mitre.org/data/definitions/1021.html">1021</a></td>
				</tr>
				<tr>
					<td width="20%">WASC Id</td>
					<td width="80%">15</td>
				</tr>
				<tr>
					<td width="20%">Plugin Id</td>
					<td width="80%"><a
						href="https://www.zaproxy.org/docs/alerts/10020/">10020</a></td>
				</tr>
			</table>
			<div class="spacer"></div>
		
			<table class="results">
				<tr height="24">
					<th width="20%" class="risk-1"><a
						id="10035"></a>
						<div>Low</div></th>
					<th class="risk-1">Strict-Transport-Security Header Not Set</th>
				</tr>
				<tr>
					<td width="20%">Description</td>
					<td width="80%">
							<div>HTTP Strict Transport Security (HSTS) is a web security policy mechanism whereby a web server declares that complying user agents (such as a web browser) are to interact with it using only secure HTTPS connections (i.e. HTTP layered over TLS/SSL). HSTS is an IETF standards track protocol and is specified in RFC 6797.</div>
							
						</td>
				</tr>
				<TR vAlign="top">
					<TD colspan="2"></TD>
				</TR>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://fonts.gstatic.com/s/dmmono/v15/aFTR7PB1QTsUX8KYvumzEYOtbYf-Vlg.woff2">https://fonts.gstatic.com/s/dmmono/v15/aFTR7PB1QTsUX8KYvumzEYOtbYf-Vlg.woff2</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://fonts.gstatic.com/s/dmmono/v15/aFTU7PB1QTsUX8KYthqQBK6PYK0.woff2">https://fonts.gstatic.com/s/dmmono/v15/aFTU7PB1QTsUX8KYthqQBK6PYK0.woff2</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://fonts.gstatic.com/s/dmsans/v16/rP2Yp2ywxg089UriI5-g4vlH9VoD8Cmcqbu0-K6z9mXg.woff2">https://fonts.gstatic.com/s/dmsans/v16/rP2Yp2ywxg089UriI5-g4vlH9VoD8Cmcqbu0-K6z9mXg.woff2</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/">https://www.saucedemo.com/</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/service-worker.js">https://www.saucedemo.com/service-worker.js</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/static/css/main.f6c64be5.chunk.css">https://www.saucedemo.com/static/css/main.f6c64be5.chunk.css</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/static/js/2.84a5cbf3.chunk.js">https://www.saucedemo.com/static/js/2.84a5cbf3.chunk.js</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/static/js/main.018d2d1e.chunk.js">https://www.saucedemo.com/static/js/main.018d2d1e.chunk.js</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://events.backtrace.io/api/summed-events/submit?universe=UNIVERSE&amp;token=TOKEN">https://events.backtrace.io/api/summed-events/submit?universe=UNIVERSE&amp;token=TOKEN</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">OPTIONS</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://events.backtrace.io/api/unique-events/submit?universe=UNIVERSE&amp;token=TOKEN">https://events.backtrace.io/api/unique-events/submit?universe=UNIVERSE&amp;token=TOKEN</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">OPTIONS</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://events.backtrace.io/api/summed-events/submit?universe=UNIVERSE&amp;token=TOKEN">https://events.backtrace.io/api/summed-events/submit?universe=UNIVERSE&amp;token=TOKEN</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">POST</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://events.backtrace.io/api/unique-events/submit?universe=UNIVERSE&amp;token=TOKEN">https://events.backtrace.io/api/unique-events/submit?universe=UNIVERSE&amp;token=TOKEN</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">POST</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
				<tr>
					<td width="20%">Instances</td>
					<td width="80%">12</td>
				</tr>
				<tr>
					<td width="20%">Solution</td>
					<td width="80%">
							<div>Ensure that your web server, application server, load balancer, etc. is configured to enforce Strict-Transport-Security.</div>
							
						</td>
				</tr>
				<tr>
					<td width="20%">Reference</td>
					<td width="80%">
							<a href="https://cheatsheetseries.owasp.org/cheatsheets/HTTP_Strict_Transport_Security_Cheat_Sheet.html">https://cheatsheetseries.owasp.org/cheatsheets/HTTP_Strict_Transport_Security_Cheat_Sheet.html</a>
							<br />
						
							<a href="https://owasp.org/www-community/Security_Headers">https://owasp.org/www-community/Security_Headers</a>
							<br />
						
							<a href="https://en.wikipedia.org/wiki/HTTP_Strict_Transport_Security">https://en.wikipedia.org/wiki/HTTP_Strict_Transport_Security</a>
							<br />
						
							<a href="https://caniuse.com/stricttransportsecurity">https://caniuse.com/stricttransportsecurity</a>
							<br />
						
							<a href="https://datatracker.ietf.org/doc/html/rfc6797">https://datatracker.ietf.org/doc/html/rfc6797</a>
							
						</td>
				</tr>
				<tr>
					<td width="20%">CWE Id</td>
					<td width="80%"><a
						href="https://cwe.mitre.org/data/definitions/319.html">319</a></td>
				</tr>
				<tr>
					<td width="20%">WASC Id</td>
					<td width="80%">15</td>
				</tr>
				<tr>
					<td width="20%">Plugin Id</td>
					<td width="80%"><a
						href="https://www.zaproxy.org/docs/alerts/10035/">10035</a></td>
				</tr>
			</table>
			<div class="spacer"></div>
		
			<table class="results">
				<tr height="24">
					<th width="20%" class="risk-1"><a
						id="10021"></a>
						<div>Low</div></th>
					<th class="risk-1">X-Content-Type-Options Header Missing</th>
				</tr>
				<tr>
					<td width="20%">Description</td>
					<td width="80%">
							<div>The Anti-MIME-Sniffing header X-Content-Type-Options was not set to &#39;nosniff&#39;. This allows older versions of Internet Explorer and Chrome to perform MIME-sniffing on the response body, potentially causing the response body to be interpreted and displayed as a content type other than the declared content type. Current (early 2014) and legacy versions of Firefox will use the declared content type (if one is set), rather than performing MIME-sniffing.</div>
							
						</td>
				</tr>
				<TR vAlign="top">
					<TD colspan="2"></TD>
				</TR>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/">https://www.saucedemo.com/</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%">x-content-type-options</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">This issue still applies to error type pages (401, 403, 500, etc.) as those pages are often still affected by injection issues, in which case there is still concern for browsers sniffing pages away from their actual content type.
At &quot;High&quot; threshold this scan rule will not alert on client or server error responses.</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/service-worker.js">https://www.saucedemo.com/service-worker.js</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%">x-content-type-options</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">This issue still applies to error type pages (401, 403, 500, etc.) as those pages are often still affected by injection issues, in which case there is still concern for browsers sniffing pages away from their actual content type.
At &quot;High&quot; threshold this scan rule will not alert on client or server error responses.</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/static/css/main.f6c64be5.chunk.css">https://www.saucedemo.com/static/css/main.f6c64be5.chunk.css</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%">x-content-type-options</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">This issue still applies to error type pages (401, 403, 500, etc.) as those pages are often still affected by injection issues, in which case there is still concern for browsers sniffing pages away from their actual content type.
At &quot;High&quot; threshold this scan rule will not alert on client or server error responses.</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/static/js/2.84a5cbf3.chunk.js">https://www.saucedemo.com/static/js/2.84a5cbf3.chunk.js</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%">x-content-type-options</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">This issue still applies to error type pages (401, 403, 500, etc.) as those pages are often still affected by injection issues, in which case there is still concern for browsers sniffing pages away from their actual content type.
At &quot;High&quot; threshold this scan rule will not alert on client or server error responses.</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/static/js/main.018d2d1e.chunk.js">https://www.saucedemo.com/static/js/main.018d2d1e.chunk.js</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%">x-content-type-options</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">This issue still applies to error type pages (401, 403, 500, etc.) as those pages are often still affected by injection issues, in which case there is still concern for browsers sniffing pages away from their actual content type.
At &quot;High&quot; threshold this scan rule will not alert on client or server error responses.</td>
					</tr>
				
				<tr>
					<td width="20%">Instances</td>
					<td width="80%">5</td>
				</tr>
				<tr>
					<td width="20%">Solution</td>
					<td width="80%">
							<div>Ensure that the application/web server sets the Content-Type header appropriately, and that it sets the X-Content-Type-Options header to &#39;nosniff&#39; for all web pages.</div>
							<br />
						
							<div>If possible, ensure that the end user uses a standards-compliant and modern web browser that does not perform MIME-sniffing at all, or that can be directed by the web application/web server to not perform MIME-sniffing.</div>
							
						</td>
				</tr>
				<tr>
					<td width="20%">Reference</td>
					<td width="80%">
							<a href="https://learn.microsoft.com/en-us/previous-versions/windows/internet-explorer/ie-developer/compatibility/gg622941(v=vs.85)">https://learn.microsoft.com/en-us/previous-versions/windows/internet-explorer/ie-developer/compatibility/gg622941(v=vs.85)</a>
							<br />
						
							<a href="https://owasp.org/www-community/Security_Headers">https://owasp.org/www-community/Security_Headers</a>
							
						</td>
				</tr>
				<tr>
					<td width="20%">CWE Id</td>
					<td width="80%"><a
						href="https://cwe.mitre.org/data/definitions/693.html">693</a></td>
				</tr>
				<tr>
					<td width="20%">WASC Id</td>
					<td width="80%">15</td>
				</tr>
				<tr>
					<td width="20%">Plugin Id</td>
					<td width="80%"><a
						href="https://www.zaproxy.org/docs/alerts/10021/">10021</a></td>
				</tr>
			</table>
			<div class="spacer"></div>
		
			<table class="results">
				<tr height="24">
					<th width="20%" class="risk-0"><a
						id="10024"></a>
						<div>Informational</div></th>
					<th class="risk-0">Information Disclosure - Sensitive Information in URL</th>
				</tr>
				<tr>
					<td width="20%">Description</td>
					<td width="80%">
							<div>The request appeared to contain sensitive information leaked in the URL. This can violate PCI and most organizational compliance policies. You can configure the list of strings for this check to add or remove values specific to your environment.</div>
							
						</td>
				</tr>
				<TR vAlign="top">
					<TD colspan="2"></TD>
				</TR>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://events.backtrace.io/api/summed-events/submit?universe=UNIVERSE&amp;token=TOKEN">https://events.backtrace.io/api/summed-events/submit?universe=UNIVERSE&amp;token=TOKEN</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">OPTIONS</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%">token</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">token</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The URL contains potentially sensitive information. The following string was found via the pattern: token
token</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://events.backtrace.io/api/unique-events/submit?universe=UNIVERSE&amp;token=TOKEN">https://events.backtrace.io/api/unique-events/submit?universe=UNIVERSE&amp;token=TOKEN</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">OPTIONS</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%">token</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">token</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The URL contains potentially sensitive information. The following string was found via the pattern: token
token</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://events.backtrace.io/api/summed-events/submit?universe=UNIVERSE&amp;token=TOKEN">https://events.backtrace.io/api/summed-events/submit?universe=UNIVERSE&amp;token=TOKEN</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">POST</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%">token</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">token</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The URL contains potentially sensitive information. The following string was found via the pattern: token
token</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://events.backtrace.io/api/unique-events/submit?universe=UNIVERSE&amp;token=TOKEN">https://events.backtrace.io/api/unique-events/submit?universe=UNIVERSE&amp;token=TOKEN</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">POST</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%">token</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">token</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The URL contains potentially sensitive information. The following string was found via the pattern: token
token</td>
					</tr>
				
				<tr>
					<td width="20%">Instances</td>
					<td width="80%">4</td>
				</tr>
				<tr>
					<td width="20%">Solution</td>
					<td width="80%">
							<div>Do not pass sensitive information in URIs.</div>
							
						</td>
				</tr>
				<tr>
					<td width="20%">Reference</td>
					<td width="80%"></td>
				</tr>
				<tr>
					<td width="20%">CWE Id</td>
					<td width="80%"><a
						href="https://cwe.mitre.org/data/definitions/598.html">598</a></td>
				</tr>
				<tr>
					<td width="20%">WASC Id</td>
					<td width="80%">13</td>
				</tr>
				<tr>
					<td width="20%">Plugin Id</td>
					<td width="80%"><a
						href="https://www.zaproxy.org/docs/alerts/10024/">10024</a></td>
				</tr>
			</table>
			<div class="spacer"></div>
		
			<table class="results">
				<tr height="24">
					<th width="20%" class="risk-0"><a
						id="10027"></a>
						<div>Informational</div></th>
					<th class="risk-0">Information Disclosure - Suspicious Comments</th>
				</tr>
				<tr>
					<td width="20%">Description</td>
					<td width="80%">
							<div>The response appears to contain suspicious comments which may help an attacker.</div>
							
						</td>
				</tr>
				<TR vAlign="top">
					<TD colspan="2"></TD>
				</TR>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/static/js/2.84a5cbf3.chunk.js">https://www.saucedemo.com/static/js/2.84a5cbf3.chunk.js</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">query</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The following pattern was used: \bQUERY\b and was detected in likely comment: &quot;//i],[v,[d,&quot;GSA&quot;]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[v,[d,&quot;TikTok&quot;]],[/headlesschrome(?:\/([\w\.]+)| )/i],[v,[d,T+&quot;, see evidence field for the suspicious comment/snippet.</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/static/js/main.018d2d1e.chunk.js">https://www.saucedemo.com/static/js/main.018d2d1e.chunk.js</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">user</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The following pattern was used: \bUSER\b and was detected in likely comment: &quot;//z/DQAImhgEGow4gywHLD2zON8gPeN+9dk4/pQ5gJDURzty2or513awGGP/RnH2MdAsBdMurAlMbKY4DUAgQg2dsXV4vm+z4H4anb1nWQKxefHhALSfKAbS0nKADaG0&quot;, see evidence field for the suspicious comment/snippet.</td>
					</tr>
				
				<tr>
					<td width="20%">Instances</td>
					<td width="80%">2</td>
				</tr>
				<tr>
					<td width="20%">Solution</td>
					<td width="80%">
							<div>Remove all comments that return information that may help an attacker and fix any underlying problems they refer to.</div>
							
						</td>
				</tr>
				<tr>
					<td width="20%">Reference</td>
					<td width="80%"></td>
				</tr>
				<tr>
					<td width="20%">CWE Id</td>
					<td width="80%"><a
						href="https://cwe.mitre.org/data/definitions/615.html">615</a></td>
				</tr>
				<tr>
					<td width="20%">WASC Id</td>
					<td width="80%">13</td>
				</tr>
				<tr>
					<td width="20%">Plugin Id</td>
					<td width="80%"><a
						href="https://www.zaproxy.org/docs/alerts/10027/">10027</a></td>
				</tr>
			</table>
			<div class="spacer"></div>
		
			<table class="results">
				<tr height="24">
					<th width="20%" class="risk-0"><a
						id="10109"></a>
						<div>Informational</div></th>
					<th class="risk-0">Modern Web Application</th>
				</tr>
				<tr>
					<td width="20%">Description</td>
					<td width="80%">
							<div>The application appears to be a modern web application. If you need to explore it automatically then the Ajax Spider may well be more effective than the standard one.</div>
							
						</td>
				</tr>
				<TR vAlign="top">
					<TD colspan="2"></TD>
				</TR>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/">https://www.saucedemo.com/</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">&lt;script type=&quot;text/javascript&quot;&gt;!function(n){if(&quot;/&quot;===n.search[1]){var a=n.search.slice(1).split(&quot;&amp;&quot;).map((function(n){return n.replace(/~and~/g,&quot;&amp;&quot;)})).join(&quot;?&quot;);window.history.replaceState(null,null,n.pathname.slice(0,-1)+a+n.hash)}}(window.location)&lt;/script&gt;</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">No links have been found while there are scripts, which is an indication that this is a modern web application.</td>
					</tr>
				
				<tr>
					<td width="20%">Instances</td>
					<td width="80%">1</td>
				</tr>
				<tr>
					<td width="20%">Solution</td>
					<td width="80%">
							<div>This is an informational alert and so no changes are required.</div>
							
						</td>
				</tr>
				<tr>
					<td width="20%">Reference</td>
					<td width="80%"></td>
				</tr>
				<tr>
					<td width="20%">CWE Id</td>
					<td width="80%"></td>
				</tr>
				<tr>
					<td width="20%">WASC Id</td>
					<td width="80%"></td>
				</tr>
				<tr>
					<td width="20%">Plugin Id</td>
					<td width="80%"><a
						href="https://www.zaproxy.org/docs/alerts/10109/">10109</a></td>
				</tr>
			</table>
			<div class="spacer"></div>
		
			<table class="results">
				<tr height="24">
					<th width="20%" class="risk-0"><a
						id="10015"></a>
						<div>Informational</div></th>
					<th class="risk-0">Re-examine Cache-control Directives</th>
				</tr>
				<tr>
					<td width="20%">Description</td>
					<td width="80%">
							<div>The cache-control header has not been set properly or is missing, allowing the browser and proxies to cache content. For static assets like css, js, or image files this might be intended, however, the resources should be reviewed to ensure that no sensitive content will be cached.</div>
							
						</td>
				</tr>
				<TR vAlign="top">
					<TD colspan="2"></TD>
				</TR>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/">https://www.saucedemo.com/</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%">cache-control</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">max-age=600</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
				<tr>
					<td width="20%">Instances</td>
					<td width="80%">1</td>
				</tr>
				<tr>
					<td width="20%">Solution</td>
					<td width="80%">
							<div>For secure content, ensure the cache-control HTTP header is set with &quot;no-cache, no-store, must-revalidate&quot;. If an asset should be cached consider setting the directives &quot;public, max-age, immutable&quot;.</div>
							
						</td>
				</tr>
				<tr>
					<td width="20%">Reference</td>
					<td width="80%">
							<a href="https://cheatsheetseries.owasp.org/cheatsheets/Session_Management_Cheat_Sheet.html#web-content-caching">https://cheatsheetseries.owasp.org/cheatsheets/Session_Management_Cheat_Sheet.html#web-content-caching</a>
							<br />
						
							<a href="https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Cache-Control">https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Cache-Control</a>
							<br />
						
							<a href="https://grayduck.mn/2021/09/13/cache-control-recommendations/">https://grayduck.mn/2021/09/13/cache-control-recommendations/</a>
							
						</td>
				</tr>
				<tr>
					<td width="20%">CWE Id</td>
					<td width="80%"><a
						href="https://cwe.mitre.org/data/definitions/525.html">525</a></td>
				</tr>
				<tr>
					<td width="20%">WASC Id</td>
					<td width="80%">13</td>
				</tr>
				<tr>
					<td width="20%">Plugin Id</td>
					<td width="80%"><a
						href="https://www.zaproxy.org/docs/alerts/10015/">10015</a></td>
				</tr>
			</table>
			<div class="spacer"></div>
		
			<table class="results">
				<tr height="24">
					<th width="20%" class="risk-0"><a
						id="10050"></a>
						<div>Informational</div></th>
					<th class="risk-0">Retrieved from Cache</th>
				</tr>
				<tr>
					<td width="20%">Description</td>
					<td width="80%">
							<div>The content was retrieved from a shared cache. If the response data is sensitive, personal or user-specific, this may result in sensitive information being leaked. In some cases, this may even result in a user gaining complete control of the session of another user, depending on the configuration of the caching components in use in their environment. This is primarily an issue where caching servers such as &quot;proxy&quot; caches are configured on the local network. This configuration is typically found in corporate or educational environments, for instance.</div>
							
						</td>
				</tr>
				<TR vAlign="top">
					<TD colspan="2"></TD>
				</TR>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/">https://www.saucedemo.com/</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">HIT</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/service-worker.js">https://www.saucedemo.com/service-worker.js</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">HIT</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/static/css/main.f6c64be5.chunk.css">https://www.saucedemo.com/static/css/main.f6c64be5.chunk.css</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">HIT</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/static/js/main.018d2d1e.chunk.js">https://www.saucedemo.com/static/js/main.018d2d1e.chunk.js</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">HIT</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%"></td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://fonts.gstatic.com/s/dmmono/v15/aFTR7PB1QTsUX8KYvumzEYOtbYf-Vlg.woff2">https://fonts.gstatic.com/s/dmmono/v15/aFTR7PB1QTsUX8KYvumzEYOtbYf-Vlg.woff2</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">Age: 13157</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The presence of the &#39;Age&#39; header indicates that a HTTP/1.1 compliant caching server is in use.</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://fonts.gstatic.com/s/dmmono/v15/aFTU7PB1QTsUX8KYthqQBK6PYK0.woff2">https://fonts.gstatic.com/s/dmmono/v15/aFTU7PB1QTsUX8KYthqQBK6PYK0.woff2</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">Age: 8911</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The presence of the &#39;Age&#39; header indicates that a HTTP/1.1 compliant caching server is in use.</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://fonts.gstatic.com/s/dmsans/v16/rP2Yp2ywxg089UriI5-g4vlH9VoD8Cmcqbu0-K6z9mXg.woff2">https://fonts.gstatic.com/s/dmsans/v16/rP2Yp2ywxg089UriI5-g4vlH9VoD8Cmcqbu0-K6z9mXg.woff2</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">Age: 8947</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The presence of the &#39;Age&#39; header indicates that a HTTP/1.1 compliant caching server is in use.</td>
					</tr>
				
					<tr>
						<td width="20%"
							class="indent1">URL</td>
						<td width="80%"><a href="https://www.saucedemo.com/static/js/2.84a5cbf3.chunk.js">https://www.saucedemo.com/static/js/2.84a5cbf3.chunk.js</a></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Method</td>
						<td width="80%">GET</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Parameter</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Attack</td>
						<td width="80%"></td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Evidence</td>
						<td width="80%">Age: 1</td>
					</tr>
					<tr>
						<td width="20%"
							class="indent2">Other Info</td>
						<td width="80%">The presence of the &#39;Age&#39; header indicates that a HTTP/1.1 compliant caching server is in use.</td>
					</tr>
				
				<tr>
					<td width="20%">Instances</td>
					<td width="80%">8</td>
				</tr>
				<tr>
					<td width="20%">Solution</td>
					<td width="80%">
							<div>Validate that the response does not contain sensitive, personal or user-specific information. If it does, consider the use of the following HTTP response headers, to limit, or prevent the content being stored and retrieved from the cache by another user:</div>
							<br />
						
							<div>Cache-Control: no-cache, no-store, must-revalidate, private</div>
							<br />
						
							<div>Pragma: no-cache</div>
							<br />
						
							<div>Expires: 0</div>
							<br />
						
							<div>This configuration directs both HTTP 1.0 and HTTP 1.1 compliant caching servers to not store the response, and to not retrieve the response (without validation) from the cache, in response to a similar request.</div>
							
						</td>
				</tr>
				<tr>
					<td width="20%">Reference</td>
					<td width="80%">
							<a href="https://tools.ietf.org/html/rfc7234">https://tools.ietf.org/html/rfc7234</a>
							<br />
						
							<a href="https://tools.ietf.org/html/rfc7231">https://tools.ietf.org/html/rfc7231</a>
							<br />
						
							<a href="https://www.rfc-editor.org/rfc/rfc9110.html">https://www.rfc-editor.org/rfc/rfc9110.html</a>
							
						</td>
				</tr>
				<tr>
					<td width="20%">CWE Id</td>
					<td width="80%"></td>
				</tr>
				<tr>
					<td width="20%">WASC Id</td>
					<td width="80%"></td>
				</tr>
				<tr>
					<td width="20%">Plugin Id</td>
					<td width="80%"><a
						href="https://www.zaproxy.org/docs/alerts/10050/">10050</a></td>
				</tr>
			</table>
			<div class="spacer"></div>
		
	

	
		
			<h3>Sequence Details</h3>
			<sup>With the associated active scan results.</sup>

			

			<div class="spacer-lg"></div>

		
	

</body>
</html>

