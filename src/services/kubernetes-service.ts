import * as k8s from '@kubernetes/client-node';
import { ZapJobInfo } from './zap-service';

export class KubernetesService {
  private static k8sApi: k8s.BatchV1Api;
  private static coreApi: k8s.CoreV1Api;
  private static initialized = false;

  // Kubernetes configuration
  private static readonly NAMESPACE = process.env.K8S_NAMESPACE || 'default';
  private static readonly ZAP_IMAGE = process.env.ZAP_IMAGE || 'zaproxy/zap-stable:latest';
  private static readonly ZAP_API_KEY = process.env.ZAP_API_KEY || 'AgentqSuperAI';

  /**
   * Initialize Kubernetes client
   */
  static async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      const kc = new k8s.KubeConfig();
      
      // Load config based on environment
      if (process.env.NODE_ENV === 'production') {
        // In-cluster configuration for production
        kc.loadFromCluster();
      } else {
        // Local development - load from kubeconfig
        kc.loadFromDefault();
      }

      this.k8sApi = kc.makeApiClient(k8s.BatchV1Api);
      this.coreApi = kc.makeApiClient(k8s.CoreV1Api);
      this.initialized = true;

      console.log('✅ Kubernetes client initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Kubernetes client:', error);
      throw error;
    }
  }

  /**
   * Create a ZAP Job for security scanning
   */
  static async createZapJob(clientId: string): Promise<ZapJobInfo | null> {
    await this.initialize();

    try {
      const jobName = `zap-scan-${clientId}`;
      const serviceName = `zap-service-${clientId}`;
      const zapPort = 8080; // Fixed port inside the pod

      console.log(`🚀 Creating ZAP Job for CLIENT_ID: ${clientId}`);

      // Create Service first to expose the ZAP API
      const service: k8s.V1Service = {
        apiVersion: 'v1',
        kind: 'Service',
        metadata: {
          name: serviceName,
          namespace: this.NAMESPACE,
          labels: {
            app: 'zap-scan',
            clientId: clientId,
            component: 'security-testing'
          }
        },
        spec: {
          selector: {
            job: jobName
          },
          ports: [{
            port: zapPort,
            targetPort: zapPort,
            protocol: 'TCP'
          }],
          type: 'ClusterIP'
        }
      };

      await this.coreApi.createNamespacedService(this.NAMESPACE, service);
      console.log(`✅ Service created: ${serviceName}`);

      // Create Job for ZAP container
      const job: k8s.V1Job = {
        apiVersion: 'batch/v1',
        kind: 'Job',
        metadata: {
          name: jobName,
          namespace: this.NAMESPACE,
          labels: {
            app: 'zap-scan',
            clientId: clientId,
            component: 'security-testing'
          }
        },
        spec: {
          template: {
            metadata: {
              labels: {
                job: jobName,
                app: 'zap-scan',
                clientId: clientId
              }
            },
            spec: {
              restartPolicy: 'Never',
              containers: [{
                name: 'zap-container',
                image: this.ZAP_IMAGE,
                command: ['zap.sh'],
                args: [
                  '-daemon',
                  '-host', '0.0.0.0',
                  '-port', zapPort.toString(),
                  '-config', 'api.addrs.addr.name=.*',
                  '-config', 'api.addrs.addr.regex=true',
                  '-config', `api.key=${this.ZAP_API_KEY}`
                ],
                ports: [{
                  containerPort: zapPort,
                  protocol: 'TCP'
                }],
                resources: {
                  requests: {
                    memory: '1Gi',
                    cpu: '500m'
                  },
                  limits: {
                    memory: '2Gi',
                    cpu: '1'
                  }
                },
                env: [{
                  name: 'CLIENT_ID',
                  value: clientId
                }],
                // Add readiness probe to know when ZAP is ready
                readinessProbe: {
                  httpGet: {
                    path: '/JSON/core/view/version/',
                    port: zapPort,
                    httpHeaders: [{
                      name: 'X-ZAP-API-Key',
                      value: this.ZAP_API_KEY
                    }]
                  },
                  initialDelaySeconds: 30,
                  periodSeconds: 5,
                  timeoutSeconds: 10,
                  failureThreshold: 12 // Allow up to 1 minute for startup
                }
              }]
            }
          },
          backoffLimit: 1, // Don't retry failed jobs
          ttlSecondsAfterFinished: 300 // Clean up completed jobs after 5 minutes
        }
      };

      const jobResponse = await this.k8sApi.createNamespacedJob(this.NAMESPACE, job);
      console.log(`✅ ZAP Job created: ${jobName}`);

      // Wait for the pod to be ready
      const podReady = await this.waitForPodReady(jobName, 120); // 2 minute timeout
      if (!podReady) {
        throw new Error(`ZAP pod failed to become ready within timeout`);
      }

      const zapJobInfo: ZapJobInfo = {
        jobName,
        serviceName,
        namespace: this.NAMESPACE,
        port: zapPort,
        host: `http://${serviceName}.${this.NAMESPACE}.svc.cluster.local:${zapPort}`,
        clientId,
        createdAt: new Date()
      };

      console.log(`✅ ZAP Job ready for CLIENT_ID: ${clientId} at ${zapJobInfo.host}`);
      return zapJobInfo;

    } catch (error) {
      console.error(`❌ Failed to create ZAP Job for CLIENT_ID ${clientId}:`, error);
      
      // Cleanup on failure
      try {
        await this.deleteZapJob(clientId);
      } catch (cleanupError) {
        console.warn(`⚠️ Failed to cleanup after job creation failure:`, cleanupError);
      }
      
      return null;
    }
  }

  /**
   * Wait for pod to be ready
   */
  private static async waitForPodReady(jobName: string, timeoutSeconds: number): Promise<boolean> {
    const startTime = Date.now();
    const timeout = timeoutSeconds * 1000;

    while (Date.now() - startTime < timeout) {
      try {
        const pods = await this.coreApi.listNamespacedPod(
          this.NAMESPACE,
          undefined, undefined, undefined, undefined,
          `job-name=${jobName}`
        );

        if (pods.body.items.length > 0) {
          const pod = pods.body.items[0];
          const podStatus = pod.status;

          if (podStatus?.phase === 'Running') {
            // Check if all containers are ready
            const containerStatuses = podStatus.containerStatuses || [];
            const allReady = containerStatuses.every(status => status.ready);
            
            if (allReady) {
              console.log(`✅ Pod ready for job: ${jobName}`);
              return true;
            }
          }
        }

        // Wait 2 seconds before checking again
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        console.warn(`⚠️ Error checking pod status for job ${jobName}:`, error);
      }
    }

    console.error(`❌ Pod failed to become ready within ${timeoutSeconds} seconds for job: ${jobName}`);
    return false;
  }

  /**
   * Delete ZAP Job and associated resources
   */
  static async deleteZapJob(clientId: string): Promise<void> {
    await this.initialize();

    const jobName = `zap-scan-${clientId}`;
    const serviceName = `zap-service-${clientId}`;

    try {
      console.log(`🗑️ Deleting ZAP Job and Service for CLIENT_ID: ${clientId}`);

      // Delete Job
      try {
        await this.k8sApi.deleteNamespacedJob(
          jobName,
          this.NAMESPACE,
          undefined,
          undefined,
          undefined,
          undefined,
          'Background' // Delete in background
        );
        console.log(`✅ Job deleted: ${jobName}`);
      } catch (error: any) {
        if (error.response?.statusCode !== 404) {
          console.warn(`⚠️ Error deleting job ${jobName}:`, error.message);
        }
      }

      // Delete Service
      try {
        await this.coreApi.deleteNamespacedService(serviceName, this.NAMESPACE);
        console.log(`✅ Service deleted: ${serviceName}`);
      } catch (error: any) {
        if (error.response?.statusCode !== 404) {
          console.warn(`⚠️ Error deleting service ${serviceName}:`, error.message);
        }
      }

      console.log(`🧹 ZAP Job cleanup completed for CLIENT_ID: ${clientId}`);
    } catch (error) {
      console.error(`❌ Error during ZAP Job cleanup for CLIENT_ID ${clientId}:`, error);
      throw error;
    }
  }

  /**
   * List all ZAP Jobs
   */
  static async listZapJobs(): Promise<string[]> {
    await this.initialize();

    try {
      const jobs = await this.k8sApi.listNamespacedJob(
        this.NAMESPACE,
        undefined, undefined, undefined, undefined,
        'app=zap-scan'
      );

      return jobs.body.items.map(job => job.metadata?.name || '').filter(name => name);
    } catch (error) {
      console.error('❌ Error listing ZAP Jobs:', error);
      return [];
    }
  }

  /**
   * Cleanup orphaned ZAP Jobs (older than specified minutes)
   */
  static async cleanupOrphanedJobs(olderThanMinutes: number = 10): Promise<void> {
    await this.initialize();

    try {
      console.log(`🧹 Cleaning up ZAP Jobs older than ${olderThanMinutes} minutes...`);

      const jobs = await this.k8sApi.listNamespacedJob(
        this.NAMESPACE,
        undefined, undefined, undefined, undefined,
        'app=zap-scan'
      );

      const now = Date.now();
      const cutoffTime = now - (olderThanMinutes * 60 * 1000);

      for (const job of jobs.body.items) {
        const creationTime = job.metadata?.creationTimestamp;
        if (creationTime) {
          const jobTime = new Date(creationTime).getTime();
          if (jobTime < cutoffTime) {
            const jobName = job.metadata?.name;
            const clientId = job.metadata?.labels?.clientId;
            
            if (jobName && clientId) {
              console.log(`🗑️ Removing orphaned ZAP Job: ${jobName} (${((now - jobTime) / 60000).toFixed(1)}min old)`);
              await this.deleteZapJob(clientId);
            }
          }
        }
      }

      console.log('✅ Orphaned ZAP Jobs cleanup completed');
    } catch (error) {
      console.error('❌ Error during orphaned jobs cleanup:', error);
    }
  }
}
