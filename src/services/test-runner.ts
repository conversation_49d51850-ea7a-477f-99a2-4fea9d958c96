import { WebSocket } from 'ws';
import { exec, ChildProcess } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';
import { ZapService } from './zap-service';


function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function formatTestOutput(output: string): string {
  // Remove all ANSI escape sequences and control characters
  output = output
    // Remove ANSI color codes (e.g., \x1B[38;5;45;1m, \x1B[0m)
    .replace(/\x1B\[[0-9;]*m/g, '')
    // Remove cursor movement sequences (e.g., [1A[2K)
    .replace(/\[1A\[2K/g, '')
    // Remove other ANSI escape sequences
    .replace(/\x1B\[[0-9;]*[A-Za-z]/g, '')
    // Remove control characters
    .replace(/[\x00-\x1F\x7F]/g, '')
    // Clean up extra whitespace
    .trim();

  // Skip empty lines after cleaning
  if (!output) {
    return '';
  }

  // Skip debug/verbose logs that aren't useful for users
  if (output.includes('pw:api') ||
      output.includes('To open last HTML report run:') ||
      output.includes('npx playwright show-report') ||
      output.includes('playwright') ||
      output.includes('Running 1 test using 1 worker') ||
      output.includes('Default Test Name') ||
      output.includes('fonts loaded') ||
      output.includes('waiting for fonts to load') ||
      output.includes('taking page screenshot') ||
      output.includes('navigations have finished') ||
      output.includes('waiting for scheduled navigations') ||
      output.includes('done scrolling') ||
      output.includes('scrolling into view') ||
      output.includes('performing click action') ||
      output.includes('click action done') ||
      output.includes('attempting') ||
      output.includes('waiting for element to be') ||
      output.includes('element is visible, enabled') ||
      output.includes('locator resolved to') ||
      output.includes('Authenticating with:') ||
      output.includes('auth/login') ||
      output.includes('Recording completion_input') ||
      output.includes('Recording completion_output') ||
      output.includes('Successfully recorded') ||
      output.includes('Connected to WebSocket server') ||
      output.includes('Connecting to WebSocket server') ||
      output.includes('Test completed successfully') ||
      output.includes('waiting for locator')) {
    return '';
  }

  // Format test steps with emojis
  if (output.includes('Running')) {
    return `🚀 ${output}`;
  }

  if (output.includes('Step: prompt')) {
    return `🌐 ${output}`;
  }

  if (output.includes('Step: goto')) {
    return `🌐 ${output}`;
  }

  if (output.includes('Step: navigate')) {
    return `🌐 ${output}`;
  }

  if (output.includes('Step: write') || output.includes('Step: fill')) {
    return `⌨️ ${output}`;
  }

  if (output.includes('Step: click')) {
    return `🖱️ ${output}`;
  }

  if (output.includes('Step: assertText')) {
    return `🔎 ${output}`;
  }

  if (output.includes('goto')) {
    return `🌐 ${output}`;
  }

  if (output.includes('visit')) {
    return `🌐 ${output}`;
  }

  if (output.includes('click')) {
    return `🖱️ ${output}`;
  }

  if (output.includes('fill')) {
    return `⌨️ ${output}`;
  }

  if (output.includes('expect(')) {
    return `🔎 ${output}`;
  }

  // Format test results
  if (output.includes('passed') || output.includes('✓')) {
    return `✅ ${output}`;
  }

  if (output.includes('failed') || output.includes('✘')) {
    return `❌ ${output}`;
  }

  if (output.includes('Error:')) {
    return `⚠️ ${output}`;
  }

  if (output.includes('Authenticating with:')) {
    return `🔐 ${output}`;
  }

  if (output.includes('navigated to')) {
    return `🌐 ${output}`;
  }

  // Return cleaned output
  return output;
}

export class TestRunnerService {
  // Use clientId as key instead of apiKey to support multiple concurrent connections per API key
  private static activeClients = new Map<string, WebSocket>();
  private static activeProcesses = new Map<string, ChildProcess>();
  private static readonly CONNECTION_TIMEOUT = 60000; // 60 seconds timeout
  private static sentLogMessages = new Set<string>();
  private static activeTests = new Map<string, boolean>();
  private static clientHeartbeats = new Map<string, number>();
  private static readonly HEARTBEAT_INTERVAL = 30000; // 30 seconds
  private static readonly HEARTBEAT_TIMEOUT = 90000; // 90 seconds
  private static lastTestRunTimes = new Map<string, number>();
  private static testLogs = new Map<string, string[]>();
  // Map to track which clientId belongs to which apiKey
  private static clientToApiKey = new Map<string, string>();

  // Start the heartbeat checker
  static startHeartbeatChecker() {
    setInterval(() => {
      const now = Date.now();
      
      // Check each client's last heartbeat
      this.clientHeartbeats.forEach((lastHeartbeat, apiKey) => {
        if (now - lastHeartbeat > this.HEARTBEAT_TIMEOUT) {
          console.log(`Client ${apiKey} heartbeat timeout, removing client`);
          this.removeClient(apiKey);
        }
      });
    }, this.HEARTBEAT_INTERVAL);
  }

  static addClient(apiKey: string, ws: WebSocket, clientId?: string) {
    // Generate unique clientId if not provided
    const uniqueClientId = clientId || `client-${apiKey}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    console.log(`Adding client with ID: ${uniqueClientId} for API key: ${apiKey}`);

    // Store the mapping between clientId and apiKey
    this.clientToApiKey.set(uniqueClientId, apiKey);

    // Set connection timeout
    const connectionTimeout = setTimeout(() => {
      if (ws.readyState !== WebSocket.OPEN) {
        ws.terminate();
        this.removeClientById(uniqueClientId);
        console.log(`Connection timeout for client ${uniqueClientId}`);
      }
    }, this.CONNECTION_TIMEOUT);

    // Clear timeout when connection is established
    ws.once('open', () => {
      clearTimeout(connectionTimeout);
      console.log(`Client ${uniqueClientId} connected successfully`);
    });

    // Set initial heartbeat
    this.clientHeartbeats.set(uniqueClientId, Date.now());

    this.activeClients.set(uniqueClientId, ws);
    
    // Set up ping-pong to keep connection alive
    const pingInterval = setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.ping();
      } else {
        clearInterval(pingInterval);
        this.removeClientById(uniqueClientId);
      }
    }, 15000);

    // Handle pong responses
    ws.on('pong', () => {
      // Update heartbeat time
      this.clientHeartbeats.set(uniqueClientId, Date.now());
    });

    // Handle ping messages from client
    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message.toString());
        if (data.type === 'ping') {
          // Update heartbeat time
          this.clientHeartbeats.set(uniqueClientId, Date.now());

          // Send pong response
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({ type: 'pong' }));
          }
        }
      } catch (error) {
        // Not a JSON message or not a ping, ignore
      }
    });

    ws.on('error', (error) => {
      console.error(`WebSocket error for client ${uniqueClientId}:`, error);
      clearInterval(pingInterval);
      this.removeClientById(uniqueClientId);
    });

    ws.on('close', () => {
      console.log(`Client ${uniqueClientId} disconnected`);
      clearInterval(pingInterval);
      this.removeClientById(uniqueClientId);

      // Only kill the process if there are no other active clients with this API key
      const hasOtherClients = Array.from(this.clientToApiKey.values()).includes(apiKey);
      if (!hasOtherClients) {
        const process = this.activeProcesses.get(uniqueClientId);
        if (process) {
          process.kill();
          this.activeProcesses.delete(uniqueClientId);
        }
      }
    });

    return uniqueClientId; // Return the unique client ID
  }

  static removeClient(apiKey: string) {
    // Remove all clients with this API key
    const clientsToRemove: string[] = [];
    this.clientToApiKey.forEach((clientApiKey, clientId) => {
      if (clientApiKey === apiKey) {
        clientsToRemove.push(clientId);
      }
    });

    clientsToRemove.forEach(clientId => {
      this.removeClientById(clientId);
    });
  }

  static removeClientById(clientId: string) {
    const ws = this.activeClients.get(clientId);
    if (ws) {
      ws.terminate();
      this.activeClients.delete(clientId);
      this.clientToApiKey.delete(clientId);
      this.clientHeartbeats.delete(clientId);
      console.log(`Removed client ${clientId}`);
    }
  }

  // Check if a client is connected by clientId
  static isClientConnected(clientId: string): boolean {
    const ws = this.activeClients.get(clientId);
    return ws !== undefined && ws.readyState === WebSocket.OPEN;
  }

  // Check if any client is connected for an API key
  static isApiKeyConnected(apiKey: string): boolean {
    for (const [clientId, clientApiKey] of this.clientToApiKey.entries()) {
      if (clientApiKey === apiKey && this.isClientConnected(clientId)) {
        return true;
      }
    }
    return false;
  }

  // Get client WebSocket connection by clientId
  static getClientConnection(clientId: string): WebSocket | undefined {
    return this.activeClients.get(clientId);
  }

  // Get client WebSocket connection by API key (returns first found)
  static getClientConnectionByApiKey(apiKey: string): WebSocket | undefined {
    for (const [clientId, clientApiKey] of this.clientToApiKey.entries()) {
      if (clientApiKey === apiKey) {
        const ws = this.activeClients.get(clientId);
        if (ws && ws.readyState === WebSocket.OPEN) {
          return ws;
        }
      }
    }
    return undefined;
  }

  static async executeTest(apiKey: string, testData: any, clientId?: string): Promise<void> {
    // Use clientId if provided, otherwise fall back to apiKey for backward compatibility
    const connectionId = clientId || apiKey;
    let ws = this.activeClients.get(connectionId);

    // Always run the test, regardless of WebSocket connection state
    if (!ws || ws.readyState !== WebSocket.OPEN) {
      console.log(`⚠️ No active WebSocket connection found for client ${clientId}, running test in background mode`);
      console.log(`🔄 Test will complete and store results in database for later retrieval`);
      ws = undefined; // Ensure we handle the undefined case properly
    } else {
      console.log(`✅ Active WebSocket connection found for ${connectionId}, running test with real-time updates`);
    }

    // Return a Promise that resolves only when the test execution is complete
    return new Promise<void>((resolve, reject) => {
      this.executeTestInternal(apiKey, testData, ws, resolve, reject, clientId);
    });
  }

  private static async executeTestInternal(
    apiKey: string,
    testData: any,
    ws: WebSocket | undefined,
    resolve: () => void,
    reject: (error: Error) => void,
    clientId?: string
  ): Promise<void> {

    // Try to validate user JWT token with backend, fall back to JWT_SECRET if not provided
    const userJwtToken = testData.userJwtToken;
    let authToken: string;

    if (userJwtToken) {
      console.log('Validating user JWT token with backend...');
      const isAuthenticated = await this.validateUserToken(userJwtToken);
      if (isAuthenticated) {
        console.log('User authentication successful, using user JWT token');
        authToken = userJwtToken;
      } else {
        console.log('User JWT token validation failed, falling back to JWT_SECRET');
        authToken = this.generateServiceToken();
      }
    } else {
      console.log('No user JWT token provided, using JWT_SECRET fallback');
      authToken = this.generateServiceToken();
    }

    // Check if there's a recent test run that needs cleanup time
    const lastRunTime = this.lastTestRunTimes.get(apiKey);
    const now = Date.now();
    if (lastRunTime && (now - lastRunTime < 5000)) {
      // If less than 5 seconds since last test, wait a bit
      const waitTime = 5000 - (now - lastRunTime);
      console.log(`Recent test detected, waiting ${waitTime}ms for cleanup...`);
      await sleep(waitTime);
    }

    try {
      // Record this test run time
      this.lastTestRunTimes.set(apiKey, Date.now());

      // Initialize logs collection for this test
      this.testLogs.set(apiKey, []);

      // Send test start notification (if WebSocket is available)
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
          type: 'test_start',
          testCaseId: testData.testCaseId,
          tcId: testData.tcId,
          jobId: clientId // Include client ID for message filtering
        }));
      } else {
        console.log('📤 Test start notification (no WebSocket connection)');
      }

      // Use existing master.spec.ts file instead of generating a new one
      const testFilePath = path.join(process.cwd(), 'tests', 'master.spec.ts');
      console.log(`Test data for: ${testData.testCase.title}`);
      console.log(`Using existing test file: ${testFilePath}`);

      // Generate JWT token for AgentQ library authentication
      const agentqJwtToken = authToken; // Use the same token we validated earlier

      // Initialize CLIENT_ID-specific ZAP container before starting new test
      // This ensures complete isolation between different users/companies
      let zapContainer = null;
      try {
        console.log('🐳 Creating dedicated ZAP container for security testing...');
        zapContainer = await ZapService.initializeClientContainer(clientId || `client-${apiKey}`);
        if (!zapContainer) {
          throw new Error('Failed to create ZAP container');
        }
        console.log(`✅ ZAP container ready on port ${zapContainer.port}`);
      } catch (zapError) {
        console.error('❌ Failed to initialize ZAP container:', zapError);
        throw new Error(`ZAP container initialization failed: ${zapError}`);
      }

      // Set environment variables for the test
      const env = {
        ...process.env,
        TEST_DATA: JSON.stringify(testData),
        AGENTQ_API_KEY: apiKey,
        AGENTQ_TOKEN: apiKey, // AgentQ library looks for this variable
        AGENTQ_JWT_TOKEN: agentqJwtToken, // Pass JWT token to AgentQ library
        CLIENT_ID: clientId, // Pass unique client ID to prevent artifact data mixing
        TEST_CASE_ID: testData.testCaseId, // Pass test case ID as additional identifier
        ZAP_PROXY_PORT: zapContainer.port.toString(), // Pass ZAP container port for proxy
        ZAP_HOST: zapContainer.host // Pass ZAP container host
      };

      const command = `PROXY_SERVER=${zapContainer.host} npx playwright test "${testFilePath}" --reporter=line`;
      console.log(`🚀 Executing test command: ${command}`);
      console.log(`🔧 Environment variables set: CLIENT_ID=${env.CLIENT_ID}, ZAP_PROXY_PORT=${env.ZAP_PROXY_PORT}`);

      // Store detailed error information
      let detailedError = '';

      const childProcess = exec(command, {
        env,
        timeout: 10 * 60 * 1000, // 10 minute timeout for child process
        killSignal: 'SIGTERM'
      }, async (error, stdout, stderr) => {
        console.log(`🎯 Child process exec callback called for ${apiKey}`);
        console.log(`🎯 Error: ${error ? error.message : 'none'}`);
        console.log(`🎯 Exit code: ${error ? error.code : 'success'}`);
        console.log(`🎯 Signal: ${error ? error.signal : 'none'}`);
        // Clean up process reference
        this.activeProcesses.delete(apiKey);

        // Get collected logs
        const logs = this.testLogs.get(apiKey) || [];

        // Determine if this is a test execution error vs test assertion failure
        const isTestAssertionFailure = error && (
          error.message.includes('ExpectError') ||
          error.message.includes('Timed out') ||
          error.message.includes('expect(') ||
          stderr?.includes('ExpectError') ||
          stderr?.includes('Timed out') ||
          stderr?.includes('expect(')
        );

        // Job execution status vs test result status
        const jobExecutedSuccessfully = !error || isTestAssertionFailure;
        const testStatus = error && !isTestAssertionFailure ? 'failed' :
                          error && isTestAssertionFailure ? 'failed' : 'passed';

        console.log(`🔍 Test execution analysis:`, {
          hasError: !!error,
          isTestAssertionFailure,
          jobExecutedSuccessfully,
          testStatus
        });

        // CRITICAL: Resolve BullMQ Promise IMMEDIATELY to ensure job completion
        // This must happen before any other operations that might fail or hang
        if (jobExecutedSuccessfully) {
          console.log(`✅ Test job completed successfully for API key: ${apiKey}, client: ${clientId || 'unknown'}`);
          console.log(`🎯 Resolving Promise for BullMQ job completion`);
          resolve(); // Job executed successfully regardless of test outcome
        } else {
          console.error(`❌ Test job execution failed for API key: ${apiKey}, client: ${clientId || 'unknown'}:`, error);
          console.log(`🎯 Rejecting Promise for BullMQ job failure`);
          reject(new Error(error?.message || 'Test execution failed')); // Only reject for actual execution failures
        }

        // For SaaS scalability: No need to track test state per API key
        console.log(`✅ Test execution completed for API key: ${apiKey}`);

        // If there was an error, capture the detailed error message
        if (error && stderr) {
          detailedError = stderr;
          // Add the detailed error to logs if it's not already there
          if (!logs.some(log => log.includes(stderr.substring(0, 100)))) {
            logs.push(`⚠️ ${stderr}`);
          }
        }

        // Save test results to backend using authenticated token
        try {
          await this.saveTestResults(testData, logs, testStatus, authToken, detailedError);
        } catch (saveError) {
          console.error('Failed to save test results:', saveError);
        }

        // Upload video if test completed (regardless of pass/fail)
        try {
          await this.uploadTestVideo(testData, authToken);
        } catch (videoError) {
          console.error('Failed to upload test video:', videoError);
        }

        // ALWAYS generate ZAP security report (regardless of test pass/fail status)
        // This ensures security analysis is independent of functional test results
        const cleanupClientId = clientId || `client-${apiKey}`;

        try {
          console.log('🔍 Generating ZAP security report (independent of test result)...');

          // Create a timeout promise that will force cleanup
          const timeoutPromise = new Promise<null>((_, reject) => {
            setTimeout(() => {
              console.log('⏰ ZAP report generation timeout - will force cleanup...');
              reject(new Error('ZAP report generation timeout after 30 seconds'));
            }, 30000); // 30 second timeout
          });

          // Race between ZAP report generation and timeout
          const zapReportPromise = ZapService.generateSecurityReport(
            cleanupClientId,
            testData.testCaseId
          );

          const zapReport = await Promise.race([zapReportPromise, timeoutPromise]);

          if (zapReport) {
            console.log(`✅ ZAP security report generated successfully: ${zapReport.summary.totalIssues} issues found`);
            console.log(`🔒 Report isolated for CLIENT_ID: ${clientId || `client-${apiKey}`}`);

            // Send ZAP report data via WebSocket if connected
            if (ws && ws.readyState === WebSocket.OPEN) {
              ws.send(JSON.stringify({
                type: 'zap_report_generated',
                zapReport: {
                  summary: zapReport.summary,
                  vulnerabilities: zapReport.vulnerabilities.slice(0, 10), // Send first 10 for preview
                  timestamp: zapReport.timestamp
                },
                jobId: clientId
              }));
            }
          } else {
            console.warn('⚠️ ZAP security report generation returned null');
          }
        } catch (zapError) {
          console.error('❌ Failed to generate ZAP security report:', zapError);
          // Don't fail the entire test for ZAP report issues
        } finally {
          // CRITICAL: Always clean up ZAP container after ZAP report generation (success or failure)
          try {
            console.log(`🧹 Cleaning up ZAP container after ZAP report generation for CLIENT_ID: ${cleanupClientId}...`);
            console.log(`🔍 Original clientId: ${clientId}, apiKey: ${apiKey}`);
            await ZapService.removeClientContainer(cleanupClientId);
            console.log('✅ ZAP container cleanup completed after report generation');
          } catch (cleanupError) {
            console.error('⚠️ Error during ZAP container cleanup after report generation:', cleanupError);
            // Don't fail the test for cleanup issues
          }
        }

        console.log('🔍 ZAP report generation completed, proceeding to cleanup...');

        // Clean up logs from memory
        this.testLogs.delete(apiKey);

        // CRITICAL: Clean up ZAP container after test completion
        try {
          const cleanupClientId = clientId || `client-${apiKey}`;
          console.log(`🧹 Cleaning up ZAP container after test completion for CLIENT_ID: ${cleanupClientId}...`);
          console.log(`🔍 Original clientId: ${clientId}, apiKey: ${apiKey}`);
          await ZapService.removeClientContainer(cleanupClientId);
          console.log('✅ ZAP container cleanup completed');
        } catch (cleanupError) {
          console.error('⚠️ Error during ZAP container cleanup:', cleanupError);
          // Don't fail the test for cleanup issues
        }

        // Send WebSocket notification based on test result (not job execution status)
        if (testStatus === 'failed') {
          console.log(`Test assertions failed (job executed successfully): ${error?.message || 'Unknown test failure'}`);
          if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'test_complete',
              status: 'failed',
              error: error?.message || 'Test assertions failed',
              stdout,
              stderr,
              jobId: clientId // Include client ID for message filtering
            }));
          } else {
            console.log('📤 Test completion notification (test failed, no WebSocket connection)');
          }
        } else {
          console.log('Test execution and assertions completed successfully');
          if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'test_complete',
              status: 'passed',
              stdout,
              stderr,
              jobId: clientId // Include client ID for message filtering
            }));
          } else {
            console.log('📤 Test completion notification (test passed, no WebSocket connection)');
          }
        }

        // Note: BullMQ job resolution already handled above immediately after test analysis

        // Don't close the connection immediately - let the frontend handle disconnection
        // This prevents race conditions where the test_complete message is lost
        console.log(`Test execution completed for client ${apiKey}, keeping connection open for frontend`);
        console.log(`Frontend will receive test_complete message and can disconnect when ready`);
      });

      // For SaaS scalability: Don't block concurrent tests per API key
      // Each test uses isolated ZAP containers and BullMQ handles concurrency
      console.log(`🚀 Starting test execution for API key: ${apiKey} (concurrent execution enabled)`);



      // Store the process reference
      this.activeProcesses.set(apiKey, childProcess);

      // Stream output in real-time
      childProcess.stdout?.on('data', (data) => {
        const output = data.toString();
        const formattedOutput = formatTestOutput(output);

        if (formattedOutput) {
          // Check if this exact message was already sent
          if (!this.sentLogMessages.has(formattedOutput)) {
            console.log('Test output:', formattedOutput);

            // Add to logs collection
            const logs = this.testLogs.get(apiKey) || [];
            logs.push(formattedOutput);
            this.testLogs.set(apiKey, logs);

            if (ws && ws.readyState === WebSocket.OPEN) {
              ws.send(JSON.stringify({
                type: 'test_output',
                output: formattedOutput,
                jobId: clientId // Include client ID for message filtering
              }));
            }

            // Add to the set of sent messages
            this.sentLogMessages.add(formattedOutput);
          }
        }
      });

      childProcess.stderr?.on('data', (data) => {
        const output = data.toString();
        const formattedOutput = formatTestOutput(output);

        if (formattedOutput) {
          // Check if this exact message was already sent
          if (!this.sentLogMessages.has(formattedOutput)) {
            console.log('Test error:', formattedOutput);

            // Add to logs collection
            const logs = this.testLogs.get(apiKey) || [];
            logs.push(`ERROR: ${formattedOutput}`);
            this.testLogs.set(apiKey, logs);

            if (ws && ws.readyState === WebSocket.OPEN) {
              ws.send(JSON.stringify({
                type: 'test_output',
                output: formattedOutput,
                isError: true,
                jobId: clientId // Include client ID for message filtering
              }));
            }

            // Add to the set of sent messages
            this.sentLogMessages.add(formattedOutput);
          }
        }
      });

      // Clear the sent messages set when the test completes
      childProcess.on('close', async (code, signal) => {
        console.log(`🔚 Child process closed with code ${code}, signal ${signal} for client ${apiKey}`);
        this.sentLogMessages.clear();
        console.log(`🧹 Process cleanup completed for API key: ${apiKey}`);

        // Note: Test completion and Promise resolution is handled in the exec callback above
        // This is just for cleanup
        console.log(`🧹 Cleanup completed for child process ${apiKey}`);
      });

      // Add error handler for child process
      childProcess.on('error', (error) => {
        console.error(`🚨 Child process error for ${apiKey}:`, error);
      });

      // Add exit handler for child process
      childProcess.on('exit', (code, signal) => {
        console.log(`🚪 Child process exited with code ${code}, signal ${signal} for client ${apiKey}`);
      });

    } catch (error) {
      console.error('Error in executeTest:', error);

      // CRITICAL: Clean up ZAP container even on test failure
      try {
        const cleanupClientId = clientId || `client-${apiKey}`;
        console.log(`🧹 Cleaning up ZAP container after test error for CLIENT_ID: ${cleanupClientId}...`);
        console.log(`🔍 Original clientId: ${clientId}, apiKey: ${apiKey}`);
        await ZapService.removeClientContainer(cleanupClientId);
        console.log('✅ ZAP container cleanup completed after error');
      } catch (cleanupError) {
        console.error('⚠️ Error during ZAP container cleanup after test failure:', cleanupError);
      }

      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
          type: 'test_error',
          message: error instanceof Error ? error.message : 'Failed to execute test',
          jobId: clientId // Include client ID for message filtering
        }));
      } else {
        console.log('📤 Test error notification (no WebSocket connection)');
      }

      // Reject the Promise for BullMQ
      reject(error instanceof Error ? error : new Error('Failed to execute test'));
    }
  }



  static handleClientDisconnect(apiKey: string): void {
    // Check if there's an active test for this client
    if (this.activeTests.get(apiKey)) {
      console.log(`Client ${apiKey} disconnected but test is still running. Allowing test to complete.`);
      // Don't kill the process, let it complete
    } else {
      // If no active test or test already completed, clean up
      const process = this.activeProcesses.get(apiKey);
      if (process) {
        console.log(`Cleaning up process for disconnected client ${apiKey}`);
        process.kill();
        this.activeProcesses.delete(apiKey);
      }
    }
    
    // Make sure to clean up any other resources
    this.sentLogMessages.clear();
    console.log(`🧹 Client disconnect cleanup completed for API key: ${apiKey}`);

    // Clean up logs for this client
    this.testLogs.delete(apiKey);

    // CRITICAL: Clean up any orphaned ZAP containers for this client
    // This handles cases where client disconnects before test completion
    setTimeout(async () => {
      try {
        console.log(`🧹 Checking for orphaned ZAP containers for disconnected client ${apiKey}...`);
        await ZapService.removeClientContainer(`client-${apiKey}`);
        console.log(`✅ Orphaned ZAP container cleanup completed for ${apiKey}`);
      } catch (cleanupError) {
        console.log(`📝 No orphaned ZAP container found for ${apiKey} (this is normal)`);
      }
    }, 5000); // Wait 5 seconds to allow test completion
  }

  static canRunTest(apiKey: string): boolean {
    // For SaaS scalability: Allow multiple concurrent tests per API key
    // Each test uses isolated ZAP containers, so there's no resource conflict
    // The BullMQ queue system handles concurrency limits (set to 50)
    console.log(`🔍 canRunTest check for API key: ${apiKey} - ALWAYS ALLOWED for SaaS scalability`);
    return true;
  }

  // Generate service JWT token using JWT_SECRET
  private static generateServiceToken(): string {
    const jwtSecret = process.env.JWT_SECRET || 'your_jwt_secret_key_change_in_production';
    const jwt = require('jsonwebtoken');
    return jwt.sign(
      {
        sub: 'test-runner-service',
        role: 'service'
      },
      jwtSecret,
      { expiresIn: '1h' }
    );
  }

  // Validate user JWT token with backend
  private static async validateUserToken(userJwtToken: string): Promise<boolean> {
    try {
      const backendUrl = process.env.AGENTQ_API_URL || 'http://localhost:3010';

      // Try to validate the token by calling the profile endpoint
      const response = await axios.get(`${backendUrl}/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${userJwtToken}`
        },
        timeout: 5000
      });

      if (response.status === 200 && response.data) {
        console.log('User JWT token validation successful:', response.data.email || 'Unknown user');
        return true;
      }

      return false;
    } catch (error) {
      console.error('User JWT token validation failed:', error);

      // If profile endpoint doesn't exist, try login verification endpoint
      try {
        const backendUrl = process.env.AGENTQ_API_URL || 'http://localhost:3010';
        const verifyResponse = await axios.post(`${backendUrl}/auth/verify`, {}, {
          headers: {
            'Authorization': `Bearer ${userJwtToken}`
          },
          timeout: 5000
        });

        if (verifyResponse.status === 200) {
          console.log('User JWT token verification successful');
          return true;
        }
      } catch (verifyError) {
        console.error('User JWT token verification also failed:', verifyError);
      }

      return false;
    }
  }

  // Save test results to backend API
  private static async saveTestResults(
    testData: any,
    logs: string[],
    status: string,
    authToken: string,
    detailedError: string = ''
  ): Promise<void> {
    try {
      const backendUrl = process.env.AGENTQ_API_URL || 'http://localhost:3010';

      // Use the provided auth token for backend authentication
      console.log('Using auth token for backend authentication');
      const token = authToken;

      // Helper function to validate UUID format
      const isValidUUID = (uuid: string): boolean => {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        return uuidRegex.test(uuid);
      };

      // Extract test duration from logs
      let duration: number | null = null;
      for (const log of logs) {
        // Look for patterns like "✅ 1 passed (3.3s)" or "Test completed in 5.2s" or "❌ 1 failed (2.1s)"
        const durationMatch = log.match(/(?:passed|completed|finished|failed|took)\s*\(?([\d.]+)s\)?/i);
        if (durationMatch && durationMatch[1]) {
          // Convert to milliseconds and store
          duration = Math.round(parseFloat(durationMatch[1]) * 1000);
          console.log(`Extracted test duration: ${duration}ms from log: "${log}"`);
          break;
        }
      }

      // Debug: Log the received test data structure
      console.log('Received testData structure:', {
        testCaseId: testData.testCaseId,
        tcId: testData.tcId,
        projectId: testData.projectId,
        testCaseProjectId: testData.testCase?.projectId,
        hasTestCase: !!testData.testCase,
        testCaseKeys: testData.testCase ? Object.keys(testData.testCase) : [],
        duration: duration
      });

      // Ensure we have valid UUIDs
      const testCaseId = testData.testCaseId && isValidUUID(testData.testCaseId)
        ? testData.testCaseId
        : "00000000-0000-0000-0000-000000000001";

      // Try to get projectId from multiple sources
      let projectId = "00000000-0000-0000-0000-000000000000";

      // First check if projectId is directly available
      if (testData.projectId && isValidUUID(testData.projectId)) {
        projectId = testData.projectId;
      } 
      // Then check if it's in the testCase object
      else if (testData.testCase?.projectId && isValidUUID(testData.testCase.projectId)) {
        projectId = testData.testCase.projectId;
      }
      // If still not found, try to fetch it from the API using the testCaseId
      else if (testCaseId && testCaseId !== "00000000-0000-0000-0000-000000000001") {
        try {
          // Make a request to get the test case details
          const response = await axios.get(`${backendUrl}/test-cases/${testCaseId}`, {
            headers: {
              'Authorization': `Bearer ${token}`
            },
            timeout: 5000
          });
          
          if (response.data && response.data.projectId && isValidUUID(response.data.projectId)) {
            projectId = response.data.projectId;
            console.log(`Retrieved projectId ${projectId} from API for testCaseId ${testCaseId}`);
          }
        } catch (error) {
          console.error(`Failed to fetch projectId for testCaseId ${testCaseId}:`, error);
        }
      }

      console.log('Using projectId:', projectId, 'from:', {
        direct: testData.projectId,
        fromTestCase: testData.testCase?.projectId,
        fromAPI: projectId !== testData.projectId && projectId !== testData.testCase?.projectId ? projectId : undefined
      });

      // Extract error message if test failed
      let errorMessage = null;
      if (status === 'failed') {
        // First use the detailed error if available
        if (detailedError) {
          // Function to strip ANSI color codes
          const stripAnsi = (str: string) => {
            return str.replace(/[\u001b\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nqry=><]/g, '');
          };
          
          // Clean the detailed error
          const cleanedError = stripAnsi(detailedError);
          
          // Look for the most important parts of the error using regex
          const timedOutMatch = cleanedError.match(/Timed out .+expect\(.+\)\.toHaveText\(.+\)([\s\S]*?)Expected string: "([^"]+)"([\s\S]*?)Received string: "([^"]+)"/);
          if (timedOutMatch) {
            errorMessage = `Test failed: Timed out waiting for element to have text.\nExpected: "${timedOutMatch[2]}"\nActual: "${timedOutMatch[4]}"`;
          } else {
            // If no specific pattern match, extract the most useful lines
            const errorLines = cleanedError.split('\n').filter(line => 
              !line.includes('at /Users/') && 
              !line.includes('node:internal/') &&
              !line.includes('at Object.<anonymous>') &&
              !line.includes('at processTicksAndRejections') &&
              !line.includes('matcherResult:') &&
              !line.includes('Symbol(step)') &&
              !line.includes('stepId:') &&
              !line.includes('function:') &&
              !line.includes('steps:') &&
              !line.trim().startsWith('    at ') &&
              !line.trim().startsWith('{') &&
              !line.trim().startsWith('}') &&
              !line.trim().startsWith('[') &&
              !line.trim().startsWith(']') &&
              line.trim() !== ''
            );
            
            // Join the filtered lines to create a clean error message
            const filteredError = errorLines.join('\n').trim();
            
            // Extract just the core error message
            const coreErrorMatch = filteredError.match(/Error: (.*?)(?:Call log:|$)/s);
            if (coreErrorMatch) {
              errorMessage = coreErrorMatch[1].trim();
            } else {
              errorMessage = filteredError;
            }
          }
          
          // If the error message is too long, truncate it
          if (errorMessage && errorMessage.length > 2000) {
            errorMessage = errorMessage.substring(0, 1997) + '...';
          }
          
          console.log('Cleaned error message:', errorMessage);
        }
        
        // If no detailed error, look for error in logs (existing code)
        if (!errorMessage) {
          // First look for detailed error messages with "Timed out" or "ExpectError"
          for (const log of logs) {
            if ((log.includes('Timed out') && log.includes('expect(')) || 
                log.includes('ExpectError:') || 
                (log.includes('Error:') && log.includes('Expected') && log.includes('Received'))) {
              
              console.log('Found detailed error message in logs:', log);
              
              // Strip ANSI color codes
              const stripAnsi = (str: string) => {
                return str.replace(/[\u001b\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nqry=><]/g, '');
              };
              
              errorMessage = stripAnsi(log);
              break;
            }
          }
          
          // If still no detailed error, look for any error message
          if (!errorMessage) {
            for (const log of logs) {
              if (log.includes('Error:') || log.includes('❌')) {
                errorMessage = log;
                break;
              }
            }
          }
        }
      }

      // Create a more minimal payload with only essential fields
      const testResultData = {
        testCaseId: testCaseId,
        tcId: testData.tcId || "TC-UNKNOWN",
        projectId: projectId,
        status: status,
        logs: logs,
        duration: duration, // Include the extracted duration
        errorMessage: errorMessage, // Include error message if test failed
        // Send a simplified version of test data to reduce payload size
        testData: {
          title: testData.testCase?.title || "Unknown Test",
          steps: testData.steps?.length || 0
        },
        executedAt: new Date().toISOString(),
        summary: `Test ${status} with ${logs.length} log entries${duration ? ` in ${duration}ms` : ''}`
      };

      console.log(`Saving test results to ${backendUrl}/temp-test-results with JWT auth`);
      
      await axios.post(`${backendUrl}/temp-test-results`, testResultData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000, // 10 second timeout
      });

      console.log(`Test results saved to backend for test case: ${testData.testCaseId}`);
    } catch (error) {
      console.error('Failed to save test results to backend:', error);
      
      // Log more detailed error information
      if (axios.isAxiosError(error) && error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
    }
  }

  // Upload test video to backend
  private static async uploadTestVideo(testData: any, authToken: string): Promise<void> {
    try {
      const fs = require('fs');
      const path = require('path');
      const FormData = require('form-data');

      // Look for video files in test-results directory
      const testResultsDir = path.join(process.cwd(), 'test-results');

      if (!fs.existsSync(testResultsDir)) {
        console.log('No test-results directory found, skipping video upload');
        return;
      }

      // Find video files using unique client ID to prevent data mixing
      const clientId = authToken; // Use authToken as fallback if clientId not available
      const testCaseId = testData.testCaseId || 'unknown';

      // Try multiple possible video paths with unique identifiers
      const possibleVideoPaths = [
        // Primary: Use client ID directory structure
        path.join(testResultsDir, clientId, 'video.webm'),
        // Secondary: Use test case ID
        path.join(testResultsDir, `TestCase-${testCaseId}`, 'video.webm'),
        // Tertiary: Use client-based test name
        path.join(testResultsDir, `Test-${clientId}`, 'video.webm'),
        // Legacy fallback patterns (for backward compatibility)
        path.join(testResultsDir, `master-${testData.tcId}`, 'video.webm'),
        path.join(testResultsDir, `master-test`, 'video.webm')
      ];

      // Search for video files with priority for unique client ID directories
      const findVideoFiles = (dir: string, clientId: string): string[] => {
        const videoFiles: string[] = [];
        try {
          const items = fs.readdirSync(dir);
          for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            if (stat.isDirectory()) {
              // Prioritize directories that match our client ID or test case ID
              if (item === clientId || item.includes(clientId) || item.includes(testCaseId)) {
                videoFiles.push(...findVideoFiles(fullPath, clientId));
              } else {
                // Still search other directories but with lower priority
                videoFiles.push(...findVideoFiles(fullPath, clientId));
              }
            } else if (item === 'video.webm') {
              videoFiles.push(fullPath);
            }
          }
        } catch (error) {
          console.error('Error reading directory:', error);
        }
        return videoFiles;
      };

      // Get all video files with client ID priority
      const allVideoFiles = findVideoFiles(testResultsDir, clientId);

      // Add the specific paths we're looking for, prioritizing unique identifier paths
      const videoFiles = [...possibleVideoPaths.filter(p => fs.existsSync(p)), ...allVideoFiles];

      if (videoFiles.length === 0) {
        console.log('No video files found for upload');
        return;
      }

      // Prioritize video files from the correct client directory, then by most recent
      const videoFile = videoFiles.sort((a, b) => {
        // First priority: files in client-specific directories
        const aInClientDir = a.includes(clientId) || a.includes(testCaseId);
        const bInClientDir = b.includes(clientId) || b.includes(testCaseId);

        if (aInClientDir && !bInClientDir) return -1;
        if (!aInClientDir && bInClientDir) return 1;

        // Second priority: most recent file
        const statA = fs.statSync(a);
        const statB = fs.statSync(b);
        return statB.mtime.getTime() - statA.mtime.getTime();
      })[0];

      console.log(`Found video file for upload: ${videoFile} (client: ${clientId})`);

      // First, get the test result ID from the backend
      const backendUrl = process.env.AGENTQ_API_URL || 'http://localhost:3010';

      // Use the provided auth token for backend authentication
      console.log('Using auth token for video upload authentication');
      const token = authToken;

      // Get the test result by testCaseId to find the result ID
      const testResultResponse = await axios.get(
        `${backendUrl}/temp-test-results/test-case/${testData.testCaseId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          },
          timeout: 5000
        }
      );

      if (!testResultResponse.data || testResultResponse.data.length === 0) {
        console.log('No test result found for video upload');
        return;
      }

      // Get the most recent test result
      const testResult = testResultResponse.data[0];
      const testResultId = testResult.id;

      console.log(`Uploading video for test result ID: ${testResultId}`);

      // Read the video file
      const videoBuffer = fs.readFileSync(videoFile);

      // Create form data for video upload
      const formData = new FormData();
      formData.append('file', videoBuffer, {
        filename: 'video.webm',
        contentType: 'video/webm'
      });

      // Upload video to backend
      await axios.post(
        `${backendUrl}/temp-test-results/${testResultId}/video`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            'Authorization': `Bearer ${token}`
          },
          timeout: 30000, // 30 second timeout for video upload
          maxContentLength: 100 * 1024 * 1024, // 100MB max
          maxBodyLength: 100 * 1024 * 1024
        }
      );

      console.log(`Video uploaded successfully for test result: ${testResultId}`);

      // Clean up the video file after successful upload
      try {
        fs.unlinkSync(videoFile);
        console.log(`Cleaned up video file: ${videoFile}`);

        // Also clean up the client-specific directory if it becomes empty
        const videoDir = path.dirname(videoFile);
        if (videoDir.includes(clientId) || videoDir.includes(testCaseId)) {
          try {
            const remainingFiles = fs.readdirSync(videoDir);
            if (remainingFiles.length === 0) {
              fs.rmdirSync(videoDir);
              console.log(`Cleaned up empty client directory: ${videoDir}`);
            }
          } catch (dirCleanupError) {
            console.log('Client directory not empty or already cleaned up');
          }
        }
      } catch (cleanupError) {
        console.error('Failed to clean up video file:', cleanupError);
      }

    } catch (error) {
      console.error('Failed to upload test video:', error);

      // Log more detailed error information
      if (axios.isAxiosError(error) && error.response) {
        console.error('Video upload response status:', error.response.status);
        console.error('Video upload response data:', error.response.data);
      }
    }
  }

  // Add a method to handle test execution with retries
  static async executeTestWithRetries(apiKey: string, testData: any, maxRetries = 3): Promise<void> {
    let retries = 0;
    
    while (retries <= maxRetries) {
      try {
        await this.executeTest(apiKey, testData);
        return; // Success, exit the loop
      } catch (error) {
        retries++;
        
        if (retries > maxRetries) {
          // Max retries reached, rethrow the error
          throw error;
        }
        
        // Wait before retrying
        const delay = 1000 * Math.pow(2, retries - 1); // Exponential backoff
        console.log(`Test execution failed, retrying in ${delay}ms (${retries}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
}

// Initialize the heartbeat checker
TestRunnerService.startHeartbeatChecker();
